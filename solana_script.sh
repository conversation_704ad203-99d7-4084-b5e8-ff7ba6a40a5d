# before new keygen check wallet.json and Before creating a keygen, check if the wallet in wallet.json is the one you want.
# But if you want to create a new one, delete the old wallet.json file first. 
# Or export the wallet address of our marketplace wallet and put it in this wallet.json file instead, then skip the keygen creation step and go to keypaire.

solana-keygen new --outfile app/wallet.json
solana config set --keypair app/wallet.json
solana config set --url https://api.devnet.solana.com
#solana config set --url https://api.mainnet-beta.solana.com
# solana config set --url https://api.testnet.solana.com

solana airdrop 2
solana balance
solana address
solana config get

docker build -t solana-nft-minter .

docker run -p 5000:5000 \
           -v "$(pwd)/wallet.json:/app/wallet.json:ro" \
           -e SOLANA_RPC_URL="https://api.devnet.solana.com" \
           --name my-nft-minter-app \
           solana-nft-minter

docker run -it --rm \
  -v $HOME/.config/solana:/root/.config/solana \
  -v $HOME/.cache/solana:/root/.cache/solana \
  -v $HOME/.local/share/solana:/root/.local/share/solana \
  solana-nft-minter
  
# docker run -it --rm \
#   -v $HOME/.config/solana:/root/.config/solana \
#   -v $HOME/.cache/solana:/root/.cache/solana \
#   -v $HOME/.local/share/solana:/root/.local/share/solana \    

# Create NFT with curl 
# Localhost or Docker IP Address 
# Replace YOUR_HOSTED_METADATA_JSON_URI with the actual URI of your metadata JSON file
curl -X POST http://localhost:5000/create_nft \
-H "Content-Type: application/json" \
-d '{
    "name": "My API NFT",
    "symbol": "APINFT",
    "metadata_uri": "YOUR_HOSTED_METADATA_JSON_URI", # Replace with your actual URI
    "seller_fee_basis_points": 750, # 7.5%
    "is_mutable": true
}'


# Create NFT with curl custom metadata
curl -X POST http://localhost:5000/create_nft \
-H "Content-Type: application/json" \
-d '{
    "name": "My Single NFT Name",
    "symbol": "SINGLENFT",
    "metadata_uri": "https://ipsf.com/metadata.json", # Replace with your actual URI can you use URL RAW file github.com
    "seller_fee_basis_points": 500,
    "is_mutable": true
}'
