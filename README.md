Important Considerations before we start:

"Edit" vs. "Update On-Chain": This Node.js code will only modify the local metadata.json file. It will not interact with the Solana blockchain to update the NFT's on-chain metadata. Updating on-chain metadata is a more complex process involving a Solana wallet, transaction signing, and interacting with the Metaplex Token Metadata Program. If you need to update on-chain metadata, that's a separate task.
is_mutable Field: The is_mutable field in your pattern is primarily for the initial creation of the NFT on-chain, indicating whether the metadata can be changed later. If it's false on-chain, you won't be able to update the metadata after creation, regardless of what you set in your local JSON. This Node.js script will only change the value in the file, not enforce on-chain mutability.
Local File Operations: This script will read an existing JSON file, modify its content based on your inputs, and then write the updated content back to the same file.
Here's the Node.js code to edit your metadata.json file.

1. editMetadata.js (Main Script)

# JavaScript
    
    const fs = require('fs');
    const path = require('path');
    
    /**
     * Edits a Metaplex metadata JSON file.
     *
     * @param {string} filePath - The path to the metadata JSON file.
     * @param {object} updates - An object containing the fields to update.
     * Nested objects (like 'collection', 'properties', 'attributes')
     * will be merged, not entirely replaced, unless specified.
     * For 'attributes' and 'properties.files', new entries will be added or existing ones updated by 'trait_type' or 'uri'.
     */
    function editMetadata(filePath, updates) {
        if (!fs.existsSync(filePath)) {
            console.error(`Error: File not found at ${filePath}`);
            return;
        }
    
        try {
            let metadata = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
            console.log("Original metadata loaded.");
    
            // Apply updates
            for (const key in updates) {
                if (updates.hasOwnProperty(key)) {
                    if (key === 'attributes' && Array.isArray(updates.attributes)) {
                        // Special handling for attributes: merge or update by trait_type
                        updates.attributes.forEach(newAttr => {
                            const existingIndex = metadata.attributes.findIndex(attr => attr.trait_type === newAttr.trait_type);
                            if (existingIndex !== -1) {
                                metadata.attributes[existingIndex] = { ...metadata.attributes[existingIndex], ...newAttr };
                            } else {
                                metadata.attributes.push(newAttr);
                            }
                        });
                    } else if (key === 'properties' && typeof updates.properties === 'object' && updates.properties !== null) {
                        // Special handling for properties: merge, and for 'files' array
                        metadata.properties = { ...metadata.properties, ...updates.properties };
    
                        if (updates.properties.files && Array.isArray(updates.properties.files)) {
                            metadata.properties.files = metadata.properties.files || []; // Ensure it's an array
                            updates.properties.files.forEach(newFile => {
                                const existingIndex = metadata.properties.files.findIndex(file => file.uri === newFile.uri);
                                if (existingIndex !== -1) {
                                    metadata.properties.files[existingIndex] = { ...metadata.properties.files[existingIndex], ...newFile };
                                } else {
                                    metadata.properties.files.push(newFile);
                                }
                            });
                        }
                        if (updates.properties.creators && Array.isArray(updates.properties.creators)) {
                             metadata.properties.creators = metadata.properties.creators || [];
                             // For creators, you might want to replace entirely or handle merging carefully
                             // For simplicity here, we'll replace the creators array if provided.
                             // If you need to update by address, you'd implement similar logic as for attributes.
                             metadata.properties.creators = updates.properties.creators;
                        }
    
                    } else if (key === 'collection' && typeof updates.collection === 'object' && updates.collection !== null) {
                        // Merge collection details
                        metadata.collection = { ...metadata.collection, ...updates.collection };
                    }
                    else if (typeof metadata[key] === 'object' && metadata[key] !== null && !Array.isArray(metadata[key])) {
                        // For other nested objects (if any, though not explicitly in your pattern beyond collection/properties)
                        metadata[key] = { ...metadata[key], ...updates[key] };
                    } else {
                        // Direct assignment for simple fields
                        metadata[key] = updates[key];
                    }
                }
            }
    
            fs.writeFileSync(filePath, JSON.stringify(metadata, null, 2), 'utf8');
            console.log(`Successfully updated metadata file: ${filePath}`);
            console.log("Updated metadata content:");
            console.log(JSON.stringify(metadata, null, 2));
    
        } catch (error) {
            console.error(`Error processing metadata file: ${error.message}`);
        }
    }
    
    // --- Usage Example ---
    const METADATA_FILE = path.join(__dirname, 'your_nft_metadata.json'); // Adjust this path to your actual metadata file
    
    // --- Create a dummy metadata.json for testing if it doesn't exist ---
    // In a real scenario, you would already have this file from your NFT generation process
    if (!fs.existsSync(METADATA_FILE)) {
        console.log(`Creating dummy file for testing: ${METADATA_FILE}`);
        const dummyMetadata = {
            "name": "Initial NFT Name",
            "symbol": "INIT",
            "description": "This is an initial description.",
            "seller_fee_basis_points": 500,
            "image": "https://arweave.net/initial_image.png",
            "animation_url": null,
            "external_url": null,
            "is_mutable": true,
            "attributes": [
                { "trait_type": "Category", "value": "Initial Category" },
                { "trait_type": "Version", "value": "1.0" }
            ],
            "collection": {
                "name": "My Awesome Collection",
                "family": "NFT Series 2025"
            },
            "properties": {
                "files": [
                    { "uri": "https://arweave.net/initial_image.png", "type": "image/png" }
                ],
                "category": "image",
                "creators": [
                    { "address": "YOUR_WALLET_ADDRESS_INITIAL_1", "share": 100 }
                ]
            }
        };
        fs.writeFileSync(METADATA_FILE, JSON.stringify(dummyMetadata, null, 2), 'utf8');
        console.log("Dummy file created. Run the script again to apply updates.");
        process.exit(0); // Exit after creating dummy file
    }
    
    // --- Define the updates you want to apply ---
    const updatesToApply = {
        name: "Updated NFT Name",
        description: "This description has been updated to reflect new details or utility.",
        image: "https://your-image-hosting.com/path/to/your/new_image.png",
        seller_fee_basis_points: 750, // Change royalties to 7.50%
        is_mutable: false, // You can change this locally, but on-chain immutability takes precedence
        external_url: "https://new-project-website.com/nft/updated",
        animation_url: "https://your-animation-hosting.com/path/to/your/new_animation.mp4",
        attributes: [
            { "trait_type": "Version", "value": "2.0" }, // Update existing attribute
            { "trait_type": "New Trait", "value": "Value of New Trait" }, // Add new attribute
            { "trait_type": "Unlockable Content", "value": "Yes" } // Add/Update for Unlockable
        ],
        collection: {
            name: "My Renamed NFT Collection", // Update collection name
            // family remains same if not provided in updates, or can be updated
        },
        properties: {
            files: [
                { "uri": "https://your-image-hosting.com/path/to/your/new_image.png", "type": "image/png" },
                { "uri": "https://your-animation-hosting.com/path/to/your/new_animation.mp4", "type": "video/mp4" } // Add new file
            ],
            category: "video", // Change primary category if animation is main
            creators: [
                { "address": "YOUR_WALLET_ADDRESS_UPDATED_1", "share": 70 }, // Update or replace creators
                { "address": "ANOTHER_WALLET_ADDRESS_2", "share": 30 }
            ]
        }
        // You can add or override any top-level field here
    };
    
# --- Execute the update ---
    editMetadata(METADATA_FILE, updatesToApply);

2. How to Use:

Save the code: Save the code above as editMetadata.js in a directory.
Create your metadata.json: In the same directory as editMetadata.js, create a file named your_nft_metadata.json (or whatever you set METADATA_FILE to) with your initial NFT metadata following your provided pattern.
Tip: The script will create a dummy your_nft_metadata.json if it doesn't exist the first time you run it. Run it once, let it create the dummy, then run it again to apply your updatesToApply.
Install Node.js: If you don't have Node.js installed, download it from nodejs.org.
Run from your terminal: Navigate to the directory where you saved the files and run:
# Bash
    node editMetadata.js
    
What the script does:

Reads the file: It reads the your_nft_metadata.json file.
Parses JSON: It converts the JSON string into a JavaScript object.
Applies Updates:
For simple top-level fields (like name, symbol, description, image, is_mutable), it directly overwrites the value.
For attributes: It iterates through the updates.attributes array. If an attribute with the same trait_type already exists, it updates its value. Otherwise, it adds the new attribute.
For properties.files: Similar to attributes, it updates or adds files based on their uri.
For collection and properties (other than files and creators): It performs a shallow merge, meaning only the fields present in updates.collection or updates.properties will be changed.
For properties.creators: The current implementation replaces the entire creators array if updates.properties.creators is provided. If you need more granular control (e.g., updating a creator's share by address), you'd need to add more complex merging logic.
Writes back: It converts the updated JavaScript object back into a formatted JSON string (with 2-space indentation for readability) and writes it back to the original file.
This script provides a robust way to manage your local metadata.json files programmatically.

###Solana NFT Creator & Metadata Editor
This repository provides tools to create Solana NFTs and manage their off-chain metadata files.

###🚀 Getting Started
This guide will walk you through the process of setting up your Solana environment, preparing your NFT metadata, and deploying your NFT using the provided scripts and API.

###Setting Up Your Solana Environment
Before you begin, ensure you have the Solana CLI tools installed and configured.

###Install Solana CLI:
If you don't have it already, install the Solana command-line tools:

# Bash
    sh -c "$(curl -sSfL https://raw.githubusercontent.com/solana-labs/solana/master/install/install-linux.sh)"
(Replace install-linux.sh with install-osx.sh or install-windows.sh as appropriate for your OS).

###Configure Solana CLI for Devnet:
Set your cluster to Devnet for development and testing:

# Bash
    solana config set --url https://api.devnet.solana.com

Generate or Load Your Wallet Keypair:
You'll need a Solana wallet keypair to pay for transactions.

Generate a new keypair:
# Bash
    solana-keygen new --outfile ~/.config/solana/id.json
(Remember to back up your seed phrase!)
Load an existing keypair: If you have an existing wallet.json file, ensure it's placed in a location accessible by your application (e.g., in the /app/ directory if using Docker, or specified by SOLANA_WALLET_FILE_PATH).

Airdrop Devnet SOL:
Fund your wallet on Devnet so you can pay for transaction fees:

# Bash
    solana airdrop 2 YOUR_WALLET_ADDRESS_HERE --url devnet
(Replace YOUR_WALLET_ADDRESS_HERE with your public key).

###🛠️ Usage Steps
Follow these steps to create and manage your NFTs:

###Step 1: Prepare Your NFT Metadata
Your NFT's rich details are stored in a metadata.json file. This repository includes a Node.js script to help you manage this file locally.

###Using editMetadata.js
The editMetadata.js script allows you to easily update fields within your metadata.json file based on a specific pattern.

Place your metadata.json: Ensure your initial metadata.json file (e.g., your_nft_metadata.json) is in the same directory as editMetadata.js. If you don't have one, the script will create a dummy file on its first run.

Edit editMetadata.js: Open editMetadata.js and modify the updatesToApply object with the data you want to change for your NFT. This includes fields like name, description, image URL, attributes, collection details, and crucially, the is_mutable flag.

Run the script:
# Bash
    node editMetadata.js
This will update your local metadata.json file.

###Uploading Your Assets
Your image and animation_url (and properties.files) in the metadata.json should point to publicly accessible URLs. You can use services like:

IPFS (InterPlanetary File System): For decentralized storage. Tools like Pinata or web3.storage can help you upload and get CIDs (Content Identifiers) that form your IPFS URLs.
GitHub RAW Files: For simple hosting of static assets.
Any public web hosting service.
Important: After uploading your assets, update the image, animation_url, and properties.files URIs in your metadata.json file to reflect the new public URLs.

###Step 2: Run Solana Deployment Script
The solana_script.sh orchestrates the backend logic for NFT creation.

Ensure solana_script.sh is executable:
# Bash
    chmod +x solana_script.sh
Execute the script step-by-step:
You will likely need to adjust this script to integrate with your Python Flask application's Docker setup or direct execution. Typically, this script would handle:

Building the Docker image.
Running the Docker container, mounting your wallet file.
Ensuring the Flask application is running and accessible.
A simplified example might look like this:

Bash

    # Example solana_script.sh (adjust as per your Docker setup)
    #!/bin/bash
    
    echo "Building Docker image..."
    docker build -t solana-nft-app .

echo "Running Docker container..."
# Ensure your wallet.json is in the correct path or mounted
    docker run -d -p 5000:5000 --name nft_creator_app \
      -v /path/to/your/wallet.json:/app/wallet.json \
      -e SOLANA_RPC_URL="https://api.devnet.solana.com" \
      solana-nft-app

echo "Waiting for Flask app to start..."
sleep 10 # Give Flask app time to initialize

echo "Docker container is running. You can now call the API."
Replace /path/to/your/wallet.json with the actual path to your id.json or wallet.json file on your host machine.

Step 3: Call the NFT Creation API
Once your Flask application is running (via Docker or direct execution), you can make an API call to create your NFT.

Use curl or any API client (like Postman, Insomnia) to send a POST request to your Flask application's /create_nft endpoint.

# Bash 
    curl -X POST \
    http://localhost:5000/create_nft \
    -H 'Content-Type: application/json' \
    -d '{
      "name": "Your NFT Name",
      "symbol": "SYM",
      "metadata_uri": "https://your-hosted-metadata-url.json",
      "seller_fee_basis_points": 500,
      "is_mutable": true
    }'
    
Replace these placeholders:

    http://localhost:5000: Adjust if your Flask app is running on a different host or port.
    "Your NFT Name": The name of your NFT.
    "SYM": The symbol for your NFT.
    "https://your-hosted-metadata-url.json": This is the most crucial part. After editing your metadata.json locally, you need to upload that metadata.json file itself to a public hosting service (like Arweave, IPFS via Pinata, or even GitHub RAW for testing). The metadata_uri in your curl command should be the public URL to that hosted JSON file.
    "seller_fee_basis_points": Your desired royalties (e.g., 500 for 5%).
    "is_mutable": Set to false if you want to freeze your NFT's metadata permanently on creation (recommended for true NFTs). If true, the metadata can be updated later by the update_authority (your payer wallet).
    We hope this guide helps you in your Solana NFT journey! Happy minting!
