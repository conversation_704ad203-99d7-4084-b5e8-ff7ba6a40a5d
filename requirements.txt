# requirements.txt
flask>=2.2.2
flask-cors>=3.0.10
flask-restful>=0.3.9
flask-socketio>=5.3.2
solders>=0.14.0 # Essential for Solana-py
solana>=0.36.0 # THIS IS THE PRIMARY SOLANA PYTHON SDK (handles ATA creation/fetching)

# If your code explicitly needs lower-level SPL program bindings, use spl-core
# spl-core>=7.2.4

borsh-construct>=0.1.0 # For Solana SDK and Metaplex
# pymetaboss>=0.1.0     # For Metaplex - Check for newer versions if available
metaplex-python # For Metaplex - Check for newer versions if available
metaplex>=0.1.0       # For Metaplex - Check for newer versions if available
metaplex-spl>=0.1.0   # For Metaplex - Check for newer versions if available
metaplex-python-sdk>=0.1.0 # For Metaplex - Check for newer versions if available
python-dotenv # For local development if not using Docker env vars directly
