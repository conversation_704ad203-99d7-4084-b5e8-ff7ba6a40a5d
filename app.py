# app.py
# This is a Flask application that creates an NFT on the Solana blockchain using the Metaplex protocol.
# It uses the `solders` library for constructing transactions and instructions.
# It also uses the `solana` and `spl` libraries for interacting with the Solana blockchain.
# The application exposes a single endpoint `/create_nft` that accepts a POST request with the NFT details.
# The application is designed to be run in a Docker container, with the wallet file mounted into the container.
# The wallet file should contain the private key of the wallet that will pay for the transaction.
# The application uses environment variables to configure the Solana RPC URL and the path to the wallet file.

# pip install Flask solders solana spl-token spl-associated-token-account borsh-construct
# pip install -U solders solana spl-token spl-associated-token-account borsh-construct
# pip install -U pymetaboss
# pip install -U metaplex-python
# pip install -U metaplex
# pip install -U metaplex-python-sdk


import os
import json
from flask import Flask, request, jsonify
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.system_program import ID as SYSTEM_PROGRAM_ID
from solders.sysvar import RENT as SYSVAR_RENT_PUBKEY
from solana.rpc.api import Client
from solana.rpc.commitment import Confirmed
from solana.transaction import Transaction
from spl.token.constants import TOKEN_PROGRAM_ID
from spl.token.instructions import initialize_mint, mint_to
import spl.token.instructions as spl_token_instructions # For MintToParams, InitializeMintParams
from spl_associated_token_account.instructions import create_associated_token_account
from solders.instruction import Instruction, AccountMeta
from solders.message import MessageV0, Message
from solders.transaction import VersionedTransaction

# Metaplex Token Metadata Program ID
METADATA_PROGRAM_ID = Pubkey.from_string('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')

# Load environment variables for configuration
SOLANA_RPC_URL = os.getenv('SOLANA_RPC_URL', 'https://api.devnet.solana.com')
# Path to the wallet file, should be mounted into the Docker container
WALLET_FILE_PATH = os.getenv('SOLANA_WALLET_FILE_PATH', '/app/wallet.json')


app = Flask(__name__)

# --- Helper Functions ---
def load_payer_keypair():
    """Loads the payer keypair from the specified JSON file."""
    try:
        with open(WALLET_FILE_PATH, 'r') as f:
            private_key_list = json.load(f)
        if len(private_key_list) != 64: # Check if it's a full private key array
             # Attempt to load from solana-keygen format if not a simple byte array
            if isinstance(private_key_list, list) and all(isinstance(x, int) for x in private_key_list):
                 # This is how solana-keygen stores it
                 return Keypair.from_bytes(bytes(private_key_list))
            else: # If it's a base58 string (less ideal for files but possible)
                return Keypair.from_base58_string(str(private_key_list))

        return Keypair.from_bytes(bytes(private_key_list)) # Assuming it's a raw byte array
    except FileNotFoundError:
        app.logger.error(f"Wallet file not found at {WALLET_FILE_PATH}. Please ensure it's correctly mounted and path is set.")
        return None
    except Exception as e:
        app.logger.error(f"Error loading keypair from {WALLET_FILE_PATH}: {e}")
        return None

def get_metadata_pda(mint_pubkey: Pubkey) -> Pubkey:
    """Derives the Metaplex Metadata PDA for a given mint."""
    return Pubkey.find_program_address(
        [b"metadata", bytes(METADATA_PROGRAM_ID), bytes(mint_pubkey)],
        METADATA_PROGRAM_ID
    )[0]

# Metaplex DataV2 and Creator structs (simplified representation for serialization)
# In a real scenario, you might use a library or more robust serialization.
# For `solders`, you often construct the byte buffer directly for instruction data.

def create_metadata_instruction_data(name: str, symbol: str, uri: str, seller_fee_basis_points: int, creators: list, is_mutable: bool) -> bytes:
    """
    Serializes the data for the CreateMetadataAccountV3 instruction.
    This is a simplified serialization. For production, ensure exact byte layout
    as per Metaplex program. `solders` helps, but data packing is key.

    Instruction data structure (simplified):
    1 (instruction discriminator for CreateMetadataAccountV3, often 33)
    DataV2:
        name (String)
        symbol (String)
        uri (String)
        seller_fee_basis_points (u16)
        creators (Option<Vec<Creator>>)
        collection (Option<Collection>) - None for this example
        uses (Option<Uses>) - None for this example
    is_mutable (bool)
    collection_details (Option<CollectionDetails>) - None for this example
    """
    from borsh_construct import CStruct, String, U16, Bool, Option, Vec

    # Define Creator struct for Borsh
    creator_struct = CStruct(
        "address" / Pubkey.FIELD, # Assuming Pubkey.FIELD is compatible or use Bytes(32) and convert
        "verified" / Bool,
        "share" / U8  # u8 for share
    )
    # Define DataV2 struct for Borsh
    data_v2_struct = CStruct(
        "name" / String,
        "symbol" / String,
        "uri" / String,
        "seller_fee_basis_points" / U16,
        "creators" / Option(Vec(creator_struct)),
        "collection" / Option(String), # Placeholder, proper Collection struct is more complex
        "uses" / Option(String)        # Placeholder, proper Uses struct is more complex
    )
    # Define CreateMetadataAccountArgsV3 struct for Borsh
    instruction_args_struct = CStruct(
        "data" / data_v2_struct,
        "is_mutable" / Bool,
        "collection_details" / Option(String) # Placeholder for CollectionDetails
    )

    # Prepare creator data
    creator_data_list = []
    if creators:
        for c in creators:
            creator_data_list.append({
                "address": c["address"],
                "verified": c["verified"],
                "share": c["share"]
            })

    # Construct the arguments
    args = {
        "data": {
            "name": name,
            "symbol": symbol,
            "uri": uri,
            "seller_fee_basis_points": seller_fee_basis_points,
            "creators": creator_data_list if creator_data_list else None,
            "collection": None, # Simplified
            "uses": None        # Simplified
        },
        "is_mutable": is_mutable,
        "collection_details": None # Simplified
    }

    # Instruction discriminator for CreateMetadataAccountV3 (usually 33)
    # This might vary or be part of a larger enum if using a full SDK binding.
    # For manual construction, you need the exact discriminator byte(s).
    # Let's assume `solders.metadata` or a similar utility would handle this.
    # For now, we'll prepend a common discriminator (this is a guess, verify with Metaplex docs).
    CREATE_METADATA_V3_DISCRIMINATOR = bytes([33])

    # Serialize arguments using borsh
    # Note: This requires `borsh_construct` and careful struct definition matching Metaplex.
    # If `borsh_construct` is not available or suitable, manual byte packing is needed.
    # For this example, we'll assume a hypothetical direct way or use a library.
    # Due to complexity of direct borsh serialization here, we'll mock this part
    # and emphasize that a robust solution uses Metaplex SDKs or precise manual serialization.

    # --- Placeholder for actual Borsh serialization ---
    # This is the most complex part to do manually without a dedicated Metaplex lib in Python.
    # `pymetaboss` or `metaplex-python` (if up-to-date) would handle this.
    # For `solders`, you'd build the byte array according to Metaplex's Rust struct layout.
    #
    # Example of how you *might* start manual serialization (very simplified):
    # import struct
    # data = CREATE_METADATA_V3_DISCRIMINATOR
    # data += len(name).to_bytes(4, 'little') + name.encode('utf-8')
    # data += len(symbol).to_bytes(4, 'little') + symbol.encode('utf-8')
    # data += len(uri).to_bytes(4, 'little') + uri.encode('utf-8')
    # data += seller_fee_basis_points.to_bytes(2, 'little')
    # # ... and so on for creators, is_mutable, etc. This is highly error-prone.

    # Given the difficulty of manual Borsh for this example's scope,
    # we will use a simplified approach. In a real app, use a Metaplex library or thorough serialization.
    # The `solders.token_metadata.create_metadata_accounts_v3` function is what you'd ideally use
    # if it directly supports constructing the instruction with full DataV2.
    # As of recent `solders` versions, it provides instruction builders.

    from solders.token_metadata import (
        create_metadata_accounts_v3,
        DataV2,
        Creator as SoldersCreator,
        Collection as SoldersCollection, # If using collections
        Uses as SoldersUses # If using uses
    )

    solders_creators_list = None
    if creators:
        solders_creators_list = [
            SoldersCreator(address=c["address"], verified=c["verified"], share=c["share"])
            for c in creators
        ]

    # For simplicity, collection and uses are None.
    # In a real scenario, you might populate these.
    # collection = SoldersCollection(verified=False, key=Pubkey.default()) # Example
    # uses = SoldersUses(use_method=0, remaining=1, total=1) # Example

    instruction_args = create_metadata_accounts_v3(
        metadata_account=Pubkey.default(), # This will be replaced by the actual PDA
        mint=Pubkey.default(), # This will be replaced by the actual mint
        mint_authority=Pubkey.default(), # This will be replaced by the actual authority
        payer=Pubkey.default(), # This will be replaced by the actual payer
        update_authority=Pubkey.default(), # This will be replaced
        name=name,
        symbol=symbol,
        uri=uri,
        creators=solders_creators_list,
        seller_fee_basis_points=seller_fee_basis_points,
        primary_sale_happened=False, # Typically false on creation
        is_mutable=is_mutable,
        token_standard=0, # 0 for NonFungible. Check Metaplex docs for others.
        collection=None, # Simplified
        uses=None, # Simplified
        collection_details=None # Simplified
    )
    # The `instruction_args` from `solders` is actually the full Instruction object.
    # We need its `data` part.
    return instruction_args.data


# --- API Endpoint ---
@app.route('/create_nft', methods=['POST'])
def create_nft_endpoint():
    app.logger.info("Received request to /create_nft")
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        nft_name = data.get('name')
        nft_symbol = data.get('symbol')
        metadata_uri = data.get('metadata_uri') # URI to the hosted Metaplex JSON
        seller_fee_basis_points = data.get('seller_fee_basis_points')
        is_mutable = data.get('is_mutable', True) # Default to mutable

        if not all([nft_name, nft_symbol, metadata_uri, isinstance(seller_fee_basis_points, int)]):
            return jsonify({"error": "Missing required fields: name, symbol, metadata_uri, seller_fee_basis_points"}), 400

        app.logger.info(f"NFT Details: Name={nft_name}, Symbol={nft_symbol}, URI={metadata_uri}, Fees={seller_fee_basis_points}")

    except Exception as e:
        app.logger.error(f"Error parsing request: {e}")
        return jsonify({"error": f"Error parsing request: {e}"}), 400

    payer_keypair = load_payer_keypair()
    if not payer_keypair:
        return jsonify({"error": "Failed to load payer wallet. Check server logs."}), 500
    app.logger.info(f"Payer wallet loaded: {payer_keypair.pubkey()}")

    solana_client = Client(SOLANA_RPC_URL, commitment=Confirmed)

    try:
        # 1. Create a new Mint Account
        mint_keypair = Keypair()
        mint_pubkey = mint_keypair.pubkey()
        app.logger.info(f"New Mint Account Pubkey: {mint_pubkey}")

        # Get rent exemption for mint account
        rent_lamports = solana_client.get_minimum_balance_for_rent_exemption(
            spl_token_instructions.MINT_LEN
        ).value

        create_mint_account_ix = spl_token_instructions.create_account(
            spl_token_instructions.CreateAccountParams(
                from_pubkey=payer_keypair.pubkey(),
                new_account_pubkey=mint_pubkey,
                lamports=rent_lamports,
                space=spl_token_instructions.MINT_LEN,
                program_id=TOKEN_PROGRAM_ID
            )
        )
        # Initialize the Mint (0 decimals for NFT, payer is mint and freeze authority)
        initialize_mint_ix = spl_token_instructions.initialize_mint(
            spl_token_instructions.InitializeMintParams(
                mint=mint_pubkey,
                decimals=0, # NFTs have 0 decimals
                mint_authority=payer_keypair.pubkey(),
                freeze_authority=payer_keypair.pubkey(), # Optional, can be None
                program_id=TOKEN_PROGRAM_ID
            )
        )
        app.logger.info("Prepared create_mint_account_ix and initialize_mint_ix")

        # 2. Create Associated Token Account (ATA) for the payer to receive the NFT
        payer_ata_pubkey = spl_associated_token_account.get_associated_token_address(
            owner=payer_keypair.pubkey(),
            mint=mint_pubkey
        )
        create_ata_ix = create_associated_token_account(
            payer=payer_keypair.pubkey(),
            owner=payer_keypair.pubkey(),
            mint=mint_pubkey
        )
        app.logger.info(f"Payer ATA: {payer_ata_pubkey}. Prepared create_ata_ix.")

        # 3. Mint 1 token to the ATA
        mint_to_ix = mint_to(
            spl_token_instructions.MintToParams(
                mint=mint_pubkey,
                dest=payer_ata_pubkey,
                mint_authority=payer_keypair.pubkey(),
                amount=1, # Mint 1 token for the NFT
                program_id=TOKEN_PROGRAM_ID,
                signers=[] # Mint authority (payer) will sign the transaction
            )
        )
        app.logger.info("Prepared mint_to_ix.")

        # 4. Create Metaplex Metadata Account
        metadata_pda = get_metadata_pda(mint_pubkey)
        app.logger.info(f"Metaplex Metadata PDA: {metadata_pda}")

        creators_list = [
            {"address": payer_keypair.pubkey(), "verified": True, "share": 100}
        ]

        # Use the solders helper to build the CreateMetadataAccountV3 instruction
        from solders.token_metadata import (
            create_metadata_accounts_v3 as create_metadata_v3_instruction_builder,
            DataV2 as SoldersDataV2,
            Creator as SoldersCreator
        )

        solders_creators = [SoldersCreator(
            address=payer_keypair.pubkey(),
            verified=True, # Payer is signing, so verified
            share=100
        )]

        # Construct DataV2 for Metaplex
        data_v2 = SoldersDataV2(
            name=nft_name,
            symbol=nft_symbol,
            uri=metadata_uri,
            seller_fee_basis_points=seller_fee_basis_points,
            creators=solders_creators,
            collection=None,  # Set to None if not part of a collection
            uses=None         # Set to None if no specific uses
        )

        create_metadata_ix = create_metadata_v3_instruction_builder(
            metadata_account=metadata_pda,
            mint=mint_pubkey,
            mint_authority=payer_keypair.pubkey(),
            payer=payer_keypair.pubkey(),
            update_authority=payer_keypair.pubkey(), # Payer is also update authority
            name=nft_name,
            symbol=nft_symbol,
            uri=metadata_uri,
            creators=solders_creators,
            seller_fee_basis_points=seller_fee_basis_points,
            primary_sale_happened=False,
            is_mutable=is_mutable,
            token_standard=0, # 0 for NonFungible (standard NFT)
            collection=None,  # Simplified for this example
            uses=None,        # Simplified for this example
            collection_details=None # Simplified for this example
        )
        app.logger.info("Prepared create_metadata_ix.")


        # Assemble Transaction
        transaction = Transaction()
        transaction.add(create_mint_account_ix)
        transaction.add(initialize_mint_ix)
        transaction.add(create_ata_ix)
        transaction.add(mint_to_ix)
        transaction.add(create_metadata_ix) # Add the Metaplex instruction

        # Signers: payer and the new mint_keypair (for creating the mint account)
        signers = [payer_keypair, mint_keypair]

        # Get the latest blockhash
        blockhash_resp = solana_client.get_latest_blockhash()
        transaction.recent_blockhash = blockhash_resp.value.blockhash
        transaction.fee_payer = payer_keypair.pubkey()

        # Sign the transaction
        # transaction.sign(*signers) # This is for legacy Transaction

        # For VersionedTransaction (recommended)
        raw_message = Message.new_with_blockhash(
            instructions=transaction.instructions,
            payer_pubkey=payer_keypair.pubkey(),
            recent_blockhash=blockhash_resp.value.blockhash
        )
        versioned_tx = VersionedTransaction(raw_message, signers)


        app.logger.info("Sending transaction...")
        # resp = solana_client.send_transaction(transaction, payer_keypair, mint_keypair, opts=TxOpts(skip_preflight=True))
        resp = solana_client.send_transaction(versioned_tx, opts={"skip_preflight": False}) # Preflight helps catch errors
        tx_signature = resp.value
        app.logger.info(f"Transaction sent. Signature: {tx_signature}")

        # Confirm transaction
        solana_client.confirm_transaction(
            tx_signature,
            commitment=Confirmed,
            last_valid_block_height=blockhash_resp.value.last_valid_block_height
        )
        app.logger.info(f"Transaction confirmed: {tx_signature}")

        return jsonify({
            "message": "NFT created successfully!",
            "nft_mint_address": str(mint_pubkey),
            "associated_token_account": str(payer_ata_pubkey),
            "metadata_pda": str(metadata_pda),
            "transaction_signature": str(tx_signature),
            "explorer_url": f"https://explorer.solana.com/tx/{tx_signature}?cluster=devnet"
        }), 201

    except Exception as e:
        app.logger.error(f"Error during NFT creation: {e}", exc_info=True)
        # Try to get more specific error details if it's an RPCException
        error_details = str(e)
        if hasattr(e, 'error_msg'): # SolanaRPCException often has this
            error_details = e.error_msg
        elif hasattr(e, 'args') and e.args: # General exceptions
             error_details = str(e.args[0])

        return jsonify({"error": "Failed to create NFT", "details": error_details}), 500


if __name__ == '__main__':
    # For Docker, it's good practice to run on 0.0.0.0
    app.run(host='0.0.0.0', port=5000, debug=True) # Set debug=False for production
