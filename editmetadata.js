const fs = require('fs');
const path = require('path');

/**
 * Edits a Metaplex metadata JSON file.
 *
 * @param {string} filePath - The path to the metadata JSON file.
 * @param {object} updates - An object containing the fields to update.
 * Nested objects (like 'collection', 'properties', 'attributes')
 * will be merged, not entirely replaced, unless specified.
 * For 'attributes' and 'properties.files', new entries will be added or existing ones updated by 'trait_type' or 'uri'.
 */
function editMetadata(filePath, updates) {
    if (!fs.existsSync(filePath)) {
        console.error(`Error: File not found at ${filePath}`);
        return;
    }

    try {
        let metadata = JSON.parse(fs.readFileSync(filePath, 'utf8'));

        console.log("Original metadata loaded.");

        // Apply updates
        for (const key in updates) {
            if (updates.hasOwnProperty(key)) {
                if (key === 'attributes' && Array.isArray(updates.attributes)) {
                    // Special handling for attributes: merge or update by trait_type
                    updates.attributes.forEach(newAttr => {
                        const existingIndex = metadata.attributes.findIndex(attr => attr.trait_type === newAttr.trait_type);
                        if (existingIndex !== -1) {
                            metadata.attributes[existingIndex] = { ...metadata.attributes[existingIndex], ...newAttr };
                        } else {
                            metadata.attributes.push(newAttr);
                        }
                    });
                } else if (key === 'properties' && typeof updates.properties === 'object' && updates.properties !== null) {
                    // Special handling for properties: merge, and for 'files' array
                    metadata.properties = { ...metadata.properties, ...updates.properties };

                    if (updates.properties.files && Array.isArray(updates.properties.files)) {
                        metadata.properties.files = metadata.properties.files || []; // Ensure it's an array
                        updates.properties.files.forEach(newFile => {
                            const existingIndex = metadata.properties.files.findIndex(file => file.uri === newFile.uri);
                            if (existingIndex !== -1) {
                                metadata.properties.files[existingIndex] = { ...metadata.properties.files[existingIndex], ...newFile };
                            } else {
                                metadata.properties.files.push(newFile);
                            }
                        });
                    }
                    if (updates.properties.creators && Array.isArray(updates.properties.creators)) {
                         metadata.properties.creators = metadata.properties.creators || [];
                         // For creators, you might want to replace entirely or handle merging carefully
                         // For simplicity here, we'll replace the creators array if provided.
                         // If you need to update by address, you'd implement similar logic as for attributes.
                         metadata.properties.creators = updates.properties.creators;
                    }

                } else if (key === 'collection' && typeof updates.collection === 'object' && updates.collection !== null) {
                    // Merge collection details
                    metadata.collection = { ...metadata.collection, ...updates.collection };
                }
                else if (typeof metadata[key] === 'object' && metadata[key] !== null && !Array.isArray(metadata[key])) {
                    // For other nested objects (if any, though not explicitly in your pattern beyond collection/properties)
                    metadata[key] = { ...metadata[key], ...updates[key] };
                } else {
                    // Direct assignment for simple fields
                    metadata[key] = updates[key];
                }
            }
        }

        fs.writeFileSync(filePath, JSON.stringify(metadata, null, 2), 'utf8');
        console.log(`Successfully updated metadata file: ${filePath}`);
        console.log("Updated metadata content:");
        console.log(JSON.stringify(metadata, null, 2));

    } catch (error) {
        console.error(`Error processing metadata file: ${error.message}`);
    }
}

// --- Usage Example ---
const METADATA_FILE = path.join(__dirname, 'your_nft_metadata.json'); // Adjust this path to your actual metadata file

// --- Create a dummy metadata.json for testing if it doesn't exist ---
// In a real scenario, you would already have this file from your NFT generation process
if (!fs.existsSync(METADATA_FILE)) {
    console.log(`Creating dummy file for testing: ${METADATA_FILE}`);
    const dummyMetadata = {
        "name": "Initial NFT Name",
        "symbol": "INIT",
        "description": "This is an initial description.",
        "seller_fee_basis_points": 500,
        "image": "https://arweave.net/initial_image.png",
        "animation_url": null,
        "external_url": null,
        "is_mutable": true,
        "attributes": [
            { "trait_type": "Category", "value": "Initial Category" },
            { "trait_type": "Version", "value": "1.0" }
        ],
        "collection": {
            "name": "My Awesome Collection",
            "family": "NFT Series 2025"
        },
        "properties": {
            "files": [
                { "uri": "https://arweave.net/initial_image.png", "type": "image/png" }
            ],
            "category": "image",
            "creators": [
                { "address": "YOUR_WALLET_ADDRESS_INITIAL_1", "share": 100 }
            ]
        }
    };
    fs.writeFileSync(METADATA_FILE, JSON.stringify(dummyMetadata, null, 2), 'utf8');
    console.log("Dummy file created. Run the script again to apply updates.");
    process.exit(0); // Exit after creating dummy file
}

// --- Define the updates you want to apply ---
const updatesToApply = {
    name: "Updated NFT Name",
    description: "This description has been updated to reflect new details or utility.",
    image: "https://your-image-hosting.com/path/to/your/new_image.png",
    seller_fee_basis_points: 750, // Change royalties to 7.50%
    is_mutable: false, // You can change this locally, but on-chain immutability takes precedence
    external_url: "https://new-project-website.com/nft/updated",
    animation_url: "https://your-animation-hosting.com/path/to/your/new_animation.mp4",
    attributes: [
        { "trait_type": "Version", "value": "2.0" }, // Update existing attribute
        { "trait_type": "New Trait", "value": "Value of New Trait" }, // Add new attribute
        { "trait_type": "Unlockable Content", "value": "Yes" } // Add/Update for Unlockable
    ],
    collection: {
        name: "My Renamed NFT Collection", // Update collection name
        // family remains same if not provided in updates, or can be updated
    },
    properties: {
        files: [
            { "uri": "https://your-image-hosting.com/path/to/your/new_image.png", "type": "image/png" },
            { "uri": "https://your-animation-hosting.com/path/to/your/new_animation.mp4", "type": "video/mp4" } // Add new file
        ],
        category: "video", // Change primary category if animation is main
        creators: [
            { "address": "YOUR_WALLET_ADDRESS_UPDATED_1", "share": 70 }, // Update or replace creators
            { "address": "ANOTHER_WALLET_ADDRESS_2", "share": 30 }
        ]
    }
    // You can add or override any top-level field here
};

// --- Execute the update ---
editMetadata(METADATA_FILE, updatesToApply);