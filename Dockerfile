# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies that might be needed for some Python packages (e.g., cryptography)
# RUN apt-get update && apt-get install -y --no-install-recommends \
#    build-essential \
#    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# IF RUN MAKETPLACE can be used to run the marketplace, change app.py to solana_marketplace_client.py

COPY app.py .
# The wallet.json will be mounted at runtime, not copied here for security.

EXPOSE 5000

# Set environment variables (can be overridden at runtime)
ENV SOLANA_RPC_URL="[https://api.devnet.solana.com](https://api.devnet.solana.com)"
ENV SOLANA_WALLET_FILE_PATH="/app/wallet.json"
ENV FLASK_APP=app.py
# ENV FLASK_RUN_HOST=0.0.0.0 # Not needed if app.run specifies host

# Healthcheck (optional but good practice)
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#   CMD curl -f http://localhost:5000/ || exit 1 # Add a /health endpoint in Flask if you use this

CMD ["python", "app.py"]

