{
    "name": "Test Create NFT",
    "symbol": "TESTNFT",
    "description": "This is a very cool NFT created with my API!",
    "seller_fee_basis_points": 500, // 5%
    "image": "https://arweave.net/--your-image-uri--.png", // IMPORTANT: Replace with your actual image URL
    "animation_url": "", // Optional
    "external_url": "https://myproject.com/nft/1", // Optional
    "attributes": [
        {"trait_type": "Color", "value": "Purple"},
        {"trait_type": "Rarity", "value": "Epic"},
        {"trait_type": "Power", "value": "Super Strong"}
    ],
    "collection": { // Optional
        "name": "My Awesome Collection",
        "family": "Awesome Family"
    },
    "properties": { // Required by some marketplaces
        "files": [
            {"uri": "https://arweave.net/--your-image-uri--.png", "type": "image/png"}
            // You can add other files like animation_url if provided
        ],
        "category": "image", // "image", "video", "audio", "vr", "html"
        "creators": [
            {
                "address": "YOUR_WALLET_ADDRESS_FROM_WALLET_JSON", // Replace with your actual wallet address
                "share": 100
            }
        ]
    }
}