{
  "name": "Your Single NFT Name",
  "symbol": "YOUR_SINGL_SYM",
  "description": "Detailed description of your NFT, its story, or its utility.",
  "seller_fee_basis_points": 500, // Represents 5.00% royalties. This is also set on-chain.
  "image": "https://your-image-hosting.com/path/to/your/image.png", // URL to the main image asset
  "animation_url": "https://your-animation-hosting.com/path/to/your/animation.mp4", // Optional: URL to an animation (video, gif, glb)
  "external_url": "https://your-project-website.com/nft/123", // Optional: A URL to an external website or resource related to this NFT
  "is_mutable": false, // is Freeze Metadata Feature, Indicates if the NFT can be modified after creation (true/false)
  "attributes": [
    {
      "trait_type": "Category",
      "value": "Digital Art" // Example category
    },
    {
      "trait_type": "Sub-Category",
      "value": "Abstract" // More specific category
    },
    {
      "trait_type": "Unlockable Content", // For "Unlockable Content"
      "value": "Yes" // Could also be a URL or instructions if not too sensitive
    },
    {
      "trait_type": "Content Rating", // For "Explicit & Sensitive Content"
      "value": "Mature" // Or "Explicit", "Sensitive", "All Ages"
    },
    {
      "trait_type": "Artist",
      "value": "Artist Name"
    },
    {
      "trait_type": "Year Created",
      "value": "2025"
    }
    // Add more custom attributes as needed
  ],

  "collection": { // For "Collection"
    "name": "Name of Your NFT Collection",
    "family": "Family Name for Your Collection Series" // Optional grouping
  },

  "properties": {
    "files": [
      {
        "uri": "https://your-image-hosting.com/path/to/your/image.png",
        "type": "image/png"
      }
      // If you have an animation_url, you might list it here too:
      // {
      //   "uri": "https://your-animation-hosting.com/path/to/your/animation.mp4",
      //   "type": "video/mp4"
      // }
    ],
    "category": "image", // Primary category: "image", "video", "audio", "vr", "html"
    "creators": [
      {
        "address": "YOUR_WALLET_ADDRESS_THAT_WILL_RECEIVE_ROYALTIES", // Replace with actual Solana address
        "share": 100 // Percentage of royalties for this creator (out of 100)
      }
      // You can list multiple creators and split the share
    ]
  },

  // Note on "Supply":
  // For a standard Metaplex Non-Fungible Token (NFT), the supply is implicitly 1.
  // If this were a "Print" from a Master Edition, edition numbers would be relevant.
  // If you mean total items in a collection, that's usually descriptive rather than a direct field here for a unique NFT.

  // Note on "metadata_uri":
  // The URI pointing to *this* JSON file is actually stored on-chain as part of the NFT's metadata.
  // It's not typically a field *within* this JSON file itself, but the on-chain `uri` field will hold the link to this file.

  // Note on "Royalties":
  // The `seller_fee_basis_points` field (e.g., 500 for 5%) determines the royalty.
  // The `creators` array within `properties` specifies who receives these royalties and their respective shares.
  // This information is used by marketplaces that adhere to the Metaplex standard.

  // Custom fields for "Unlockable Content" details (if not just an attribute):
  // "unlockable_content_instructions": "Visit example.com/unlock after purchase and verify ownership.",
  // "unlockable_content_preview_url": "https://your-hosting.com/path/to/unlockable_preview.jpg"
}
