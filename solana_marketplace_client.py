
'''
How to get this running:

Save the file: Save the code above as solana_marketplace_client.py.
Ensure requirements.txt is updated: If you don't have borsh-construct (or struct if using manual serialization) in your requirements.txt, add it.
Install dependencies: pip install -r requirements.txt
Replace placeholders:
MARKETPLACE_PROGRAM_ID: CRITICAL! You must replace YourMarketplaceProgramIdHere1111111111111111111 with the actual public key of your deployed Solana smart contract.
EXAMPLE_NFT_MINT_ADDRESS: Replace with a real NFT mint address on devnet that you want to test with.
wallet.json path: Ensure SOLANA_WALLET_FILE_PATH points to your payer's keypair file.
'''


import os
import json
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.system_program import ID as SYSTEM_PROGRAM_ID
from solana.rpc.api import Client
from solana.rpc.commitment import Confirmed
from solders.instruction import Instruction, AccountMeta
from solders.message import Message
from solders.transaction import VersionedTransaction
from spl.token.constants import TOKEN_PROGRAM_ID
from spl.token.client import Token
from spl.token.instructions import get_associated_token_address
import struct # For simple serialization, though borsh-construct is better

# --- Configuration ---
SOLANA_RPC_URL = os.getenv('SOLANA_RPC_URL', 'https://api.devnet.solana.com')
WALLET_FILE_PATH = os.getenv('SOLANA_WALLET_FILE_PATH', '/app/wallet.json')

# !!! IMPORTANT !!!
# Replace this with the actual Program ID of your deployed Solana Smart Contract
# This is just a placeholder. You need to deploy your Rust program first.
MARKETPLACE_PROGRAM_ID = Pubkey.from_string('YourMarketplaceProgramIdHere1111111111111111111')

# --- Helper Functions (from your app.py) ---
def load_payer_keypair():
    """Loads the payer keypair from the specified JSON file."""
    try:
        with open(WALLET_FILE_PATH, 'r') as f:
            private_key_list = json.load(f)
        if len(private_key_list) != 64:
            if isinstance(private_key_list, list) and all(isinstance(x, int) for x in private_key_list):
                return Keypair.from_bytes(bytes(private_key_list))
            else:
                return Keypair.from_base58_string(str(private_key_list))
        return Keypair.from_bytes(bytes(private_key_list))
    except FileNotFoundError:
        print(f"Error: Wallet file not found at {WALLET_FILE_PATH}. Please ensure it's correctly mounted and path is set.")
        return None
    except Exception as e:
        print(f"Error loading keypair from {WALLET_FILE_PATH}: {e}")
        return None

# --- Instruction Data Serialization (Simplified Example) ---
# In a real program, you'd define instruction discriminators (e.g., 0 for Sell, 1 for Buy)
# and use a robust serialization library like borsh-construct to match your Rust program's structs.

# Example structure for instruction data for a hypothetical 'Sell' instruction
# Instruction: Sell (discriminator 0)
# Args: price (u64), amount (u64)
def serialize_sell_instruction_data(price: int, amount: int) -> bytes:
    # Instruction discriminator (e.g., 0 for Sell instruction)
    discriminator = bytes([0])
    # Serialize price and amount as little-endian 8-byte integers (u64)
    serialized_data = discriminator + \
                      price.to_bytes(8, 'little') + \
                      amount.to_bytes(8, 'little')
    return serialized_data

# Example structure for instruction data for a hypothetical 'Buy' instruction
# Instruction: Buy (discriminator 1)
# Args: price (u64), amount (u64)
def serialize_buy_instruction_data(price: int, amount: int) -> bytes:
    # Instruction discriminator (e.g., 1 for Buy instruction)
    discriminator = bytes([1])
    # Serialize price and amount as little-endian 8-byte integers (u64)
    serialized_data = discriminator + \
                      price.to_bytes(8, 'little') + \
                      amount.to_bytes(8, 'little')
    return serialized_data

# --- Main Functions for Buy/Sell ---

def place_sell_order(
    client: Client,
    payer: Keypair,
    nft_mint_address: Pubkey,
    price_sol: float, # Price in SOL
    amount: int = 1 # For NFT, amount is usually 1
):
    print(f"\nPlacing sell order for NFT: {nft_mint_address} at {price_sol} SOL...")

    seller_token_account = get_associated_token_address(payer.pubkey(), nft_mint_address)

    # --- Account Metas for a hypothetical Sell instruction ---
    # This structure depends entirely on your Rust program's instruction accounts.
    # Common accounts for a sell order:
    # 1. Seller's token account (from which NFT is taken) - Signer, Writable
    # 2. NFT Mint account - Readonly
    # 3. Market program's escrow account (where NFT goes) - Writable
    # 4. Seller's main account (payer) - Signer, Writable
    # 5. Token Program ID - Readonly
    # 6. System Program ID - Readonly (for rent or creating new accounts)
    # 7. Associated Token Program ID - Readonly
    # 8. Rent Sysvar - Readonly

    # Hypothetical AccountMeta setup (adjust to your actual Rust program)
    account_metas = [
        AccountMeta(pubkey=seller_token_account, is_signer=False, is_writable=True),
        AccountMeta(pubkey=nft_mint_address, is_signer=False, is_writable=False),
        AccountMeta(pubkey=payer.pubkey(), is_signer=True, is_writable=True),
        AccountMeta(pubkey=TOKEN_PROGRAM_ID, is_signer=False, is_writable=False),
        AccountMeta(pubkey=SYSTEM_PROGRAM_ID, is_signer=False, is_writable=False),
        # Add more accounts as required by your specific program's Sell instruction
        # e.g., a specific PDA for the order itself, escrow accounts, etc.
    ]

    # Convert SOL price to lamports (1 SOL = 1,000,000,000 lamports)
    price_lamports = int(price_sol * 1_000_000_000)

    # Serialize instruction data
    instruction_data = serialize_sell_instruction_data(price_lamports, amount)

    instruction = Instruction(
        program_id=MARKETPLACE_PROGRAM_ID,
        accounts=account_metas,
        data=instruction_data
    )

    recent_blockhash = client.get_latest_blockhash().value.blockhash
    message = Message.new_with_blockhash(
        instructions=[instruction],
        payer_pubkey=payer.pubkey(),
        recent_blockhash=recent_blockhash
    )
    transaction = VersionedTransaction(message, [payer])

    try:
        print("Sending sell order transaction...")
        resp = client.send_transaction(transaction, opts={"skip_preflight": False})
        tx_sig = resp.value
        print(f"Sell order transaction sent: {tx_sig}")
        client.confirm_transaction(tx_sig, commitment=Confirmed, last_valid_block_height=client.get_latest_blockhash().value.last_valid_block_height)
        print("Sell order transaction confirmed!")
        return tx_sig
    except Exception as e:
        print(f"Error placing sell order: {e}")
        # Add better error parsing here
        return None

def place_buy_order(
    client: Client,
    payer: Keypair,
    nft_mint_address: Pubkey,
    price_sol: float, # Price in SOL
    amount: int = 1
):
    print(f"\nPlacing buy order for NFT: {nft_mint_address} at {price_sol} SOL...")

    buyer_token_account = get_associated_token_address(payer.pubkey(), nft_mint_address)

    # --- Account Metas for a hypothetical Buy instruction ---
    # This structure depends entirely on your Rust program's instruction accounts.
    # Common accounts for a buy order:
    # 1. Buyer's main account (payer) - Signer, Writable (for sending SOL)
    # 2. Buyer's token account (where NFT goes) - Writable
    # 3. NFT Mint account - Readonly
    # 4. Market program's escrow account (where SOL goes) - Writable
    # 5. System Program ID - Readonly
    # 6. Token Program ID - Readonly
    # 7. Associated Token Program ID - Readonly
    # 8. Rent Sysvar - Readonly

    # Hypothetical AccountMeta setup (adjust to your actual Rust program)
    account_metas = [
        AccountMeta(pubkey=payer.pubkey(), is_signer=True, is_writable=True),
        AccountMeta(pubkey=buyer_token_account, is_signer=False, is_writable=True),
        AccountMeta(pubkey=nft_mint_address, is_signer=False, is_writable=False),
        AccountMeta(pubkey=SYSTEM_PROGRAM_ID, is_signer=False, is_writable=False),
        AccountMeta(pubkey=TOKEN_PROGRAM_ID, is_signer=False, is_writable=False),
        # Add more accounts as required by your specific program's Buy instruction
        # e.g., a specific PDA for the order itself, escrow accounts, etc.
    ]

    # Convert SOL price to lamports
    price_lamports = int(price_sol * 1_000_000_000)

    # Serialize instruction data
    instruction_data = serialize_buy_instruction_data(price_lamports, amount)

    instruction = Instruction(
        program_id=MARKETPLACE_PROGRAM_ID,
        accounts=account_metas,
        data=instruction_data
    )

    recent_blockhash = client.get_latest_blockhash().value.blockhash
    message = Message.new_with_blockhash(
        instructions=[instruction],
        payer_pubkey=payer.pubkey(),
        recent_blockhash=recent_blockhash
    )
    transaction = VersionedTransaction(message, [payer])

    try:
        print("Sending buy order transaction...")
        resp = client.send_transaction(transaction, opts={"skip_preflight": False})
        tx_sig = resp.value
        print(f"Buy order transaction sent: {tx_sig}")
        client.confirm_transaction(tx_sig, commitment=Confirmed, last_valid_block_height=client.get_latest_blockhash().value.last_valid_block_height)
        print("Buy order transaction confirmed!")
        return tx_sig
    except Exception as e:
        print(f"Error placing buy order: {e}")
        # Add better error parsing here
        return None

# --- Main Execution Block ---
if __name__ == "__main__":
    payer_keypair = load_payer_keypair()
    if not payer_keypair:
        exit(1)

    solana_client = Client(SOLANA_RPC_URL, commitment=Confirmed)
    print(f"Connected to Solana RPC: {SOLANA_RPC_URL}")
    print(f"Payer: {payer_keypair.pubkey()}")

    # !!! IMPORTANT: Replace with an actual NFT Mint Address you own or want to buy/sell !!!
    # This must be a real NFT that exists on the devnet
    EXAMPLE_NFT_MINT_ADDRESS = Pubkey.from_string("YOUR_NFT_MINT_ADDRESS_HERE") # e.g., '6B2Z3... (replace)'

    # --- Example Usage ---
    # 1. Place a hypothetical Sell Order
    # Uncomment and replace with your actual NFT mint address to test (if you have the program deployed)
    # print("\n--- Testing Place Sell Order ---")
    # sell_tx_signature = place_sell_order(
    #     solana_client,
    #     payer_keypair,
    #     EXAMPLE_NFT_MINT_ADDRESS,
    #     price_sol=0.1, # Selling for 0.1 SOL
    #     amount=1
    # )
    # if sell_tx_signature:
    #     print(f"Check sell transaction on explorer: https://explorer.solana.com/tx/{sell_tx_signature}?cluster=devnet")

    # 2. Place a hypothetical Buy Order
    # Uncomment and replace with the NFT mint address you want to buy
    # print("\n--- Testing Place Buy Order ---")
    # buy_tx_signature = place_buy_order(
    #     solana_client,
    #     payer_keypair,
    #     EXAMPLE_NFT_MINT_ADDRESS,
    #     price_sol=0.05, # Bidding 0.05 SOL
    #     amount=1
    # )
    # if buy_tx_signature:
    #     print(f"Check buy transaction on explorer: https://explorer.solana.com/tx/{buy_tx_signature}?cluster=devnet")

    print("\nNote: This script requires a deployed Solana smart contract with matching instruction discriminators and account layouts.")
    print("The examples above are placeholders and will fail without a compatible program.")